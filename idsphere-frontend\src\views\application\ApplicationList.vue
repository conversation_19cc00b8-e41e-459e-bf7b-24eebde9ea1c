<template>
  <div class="application-list">
    <!-- 搜索和操作栏 -->
    <div class="toolbar">
      <div class="search-section">
        <el-input
          v-model="queryForm.keyword"
          placeholder="搜索应用名称、描述或URL"
          :prefix-icon="Search"
          clearable
          style="width: 300px"
          @keyup.enter="handleSearch"
        />
        <el-select
          v-model="queryForm.protocolType"
          placeholder="协议类型"
          clearable
          style="width: 120px; margin-left: 12px"
        >
          <el-option label="OAuth2.0" value="oauth2" />
          <el-option label="CAS" value="cas" />
          <el-option label="SAML" value="saml" />
          <el-option label="OIDC" value="oidc" />
        </el-select>
        <el-select
          v-model="queryForm.groupId"
          placeholder="应用分组"
          clearable
          style="width: 120px; margin-left: 12px"
        >
          <el-option
            v-for="group in appGroups"
            :key="group.id"
            :label="group.name"
            :value="group.id"
          />
        </el-select>
        <el-button type="primary" :icon="Search" @click="handleSearch">
          搜索
        </el-button>
        <el-button :icon="Refresh" @click="handleReset">重置</el-button>
      </div>

      <div class="action-section">
        <el-button type="primary" :icon="Plus" @click="handleCreate">
          新增应用
        </el-button>
        <el-button type="success" @click="testDialog">
          测试对话框
        </el-button>
        <el-button :icon="Download" @click="handleExport">导出</el-button>
      </div>
    </div>

    <!-- 应用列表 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      stripe
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" />
      
      <el-table-column label="应用信息" min-width="200">
        <template #default="{ row }">
          <div class="app-info">
            <div class="app-icon">
              <img v-if="row.logo" :src="row.logo" :alt="row.name" />
              <el-icon v-else size="32"><Grid /></el-icon>
            </div>
            <div class="app-details">
              <div class="app-name">{{ row.name }}</div>
              <div class="app-description">{{ row.description }}</div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="url" label="应用地址" min-width="200" show-overflow-tooltip />

      <el-table-column label="协议类型" width="100">
        <template #default="{ row }">
          <el-tag :type="getProtocolTagType(row.protocolType)">
            {{ getProtocolName(row.protocolType) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="clientId" label="客户端ID" width="150" show-overflow-tooltip />

      <el-table-column label="状态" width="80">
        <template #default="{ row }">
          <el-switch
            v-model="row.isActive"
            @change="handleStatusChange(row)"
          />
        </template>
      </el-table-column>

      <el-table-column label="统计信息" width="120">
        <template #default="{ row }">
          <div class="stats-info">
            <div class="stat-item">
              <span class="stat-label">今日访问:</span>
              <span class="stat-value">{{ row.statistics?.todayVisits || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">总访问:</span>
              <span class="stat-value">{{ row.statistics?.totalVisits || 0 }}</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="createdAt" label="创建时间" width="160" />

      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button type="text" size="small" @click="handleView(row)">
            查看
          </el-button>
          <el-button type="text" size="small" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button type="text" size="small" @click="handleConfig(row)">
            配置
          </el-button>
          <el-button
            type="text"
            size="small"
            style="color: #f56c6c"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="queryForm.pageNum"
        v-model:page-size="queryForm.pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSearch"
        @current-change="handleSearch"
      />
    </div>

    <!-- 应用表单弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitleComputed"
      width="600px"
      @close="handleDialogClose"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="应用名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入应用名称" />
        </el-form-item>

        <el-form-item label="应用描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入应用描述"
          />
        </el-form-item>

        <el-form-item label="应用地址" prop="url">
          <el-input v-model="formData.url" placeholder="请输入应用地址" />
        </el-form-item>

        <el-form-item label="Logo地址" prop="logo">
          <el-input v-model="formData.logo" placeholder="请输入Logo地址" />
        </el-form-item>

        <el-form-item label="协议类型" prop="protocolType">
          <el-select v-model="formData.protocolType" placeholder="请选择协议类型">
            <el-option label="OAuth2.0" value="oauth2" />
            <el-option label="CAS" value="cas" />
            <el-option label="SAML" value="saml" />
            <el-option label="OIDC" value="oidc" />
          </el-select>
        </el-form-item>

        <el-form-item
          v-if="['oauth2', 'oidc'].includes(formData.protocolType)"
          label="回调地址"
          prop="redirectUri"
        >
          <el-input v-model="formData.redirectUri" placeholder="请输入回调地址" />
        </el-form-item>

        <el-form-item
          v-if="['oauth2', 'oidc'].includes(formData.protocolType)"
          label="授权范围"
          prop="scope"
        >
          <el-input v-model="formData.scope" placeholder="请输入授权范围，如：read write" />
        </el-form-item>

        <el-form-item label="应用分组" prop="groupId">
          <el-select v-model="formData.groupId" placeholder="请选择应用分组">
            <el-option
              v-for="group in appGroups"
              :key="group.id"
              :label="group.name"
              :value="group.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="formData.sort" :min="0" />
        </el-form-item>

        <el-form-item label="是否启用" prop="isActive">
          <el-switch v-model="formData.isActive" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox, ElForm } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Download,
  Grid
} from '@element-plus/icons-vue'
import { createApplication, updateApplication } from '@/api/application'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const total = ref(0)
const tableData = ref([])
const appGroups = ref([
  { id: 1, name: '办公应用' },
  { id: 2, name: '开发工具' },
  { id: 3, name: '业务系统' }
])

const formRef = ref<InstanceType<typeof ElForm>>()

// 计算属性
const dialogTitleComputed = computed(() => {
  return formData.id ? '编辑应用' : '新增应用'
})

// 查询表单
const queryForm = reactive({
  pageNum: 1,
  pageSize: 10,
  keyword: '',
  protocolType: '',
  groupId: null,
  isActive: null
})

// 表单数据
const formData = reactive({
  id: null,
  name: '',
  description: '',
  url: '',
  logo: '',
  protocolType: '',
  redirectUri: '',
  scope: '',
  groupId: null,
  sort: 0,
  isActive: true
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入应用名称', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请输入应用地址', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
  ],
  protocolType: [
    { required: true, message: '请选择协议类型', trigger: 'change' }
  ],
  redirectUri: [
    { required: true, message: '请输入回调地址', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
  ]
}

// 方法
const handleSearch = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取应用列表
    console.log('搜索应用:', queryForm)
    
    // 模拟数据
    tableData.value = [
      {
        id: 1,
        name: 'OA办公系统',
        description: '企业办公自动化系统',
        url: 'http://oa.example.com',
        logo: '/apps/oa.png',
        protocolType: 'oauth2',
        clientId: 'app_oa_12345',
        isActive: true,
        statistics: { todayVisits: 156, totalVisits: 12580 },
        createdAt: '2024-01-15 10:30:00'
      },
      {
        id: 2,
        name: 'CRM客户管理',
        description: '客户关系管理系统',
        url: 'http://crm.example.com',
        logo: '/apps/crm.png',
        protocolType: 'cas',
        clientId: 'app_crm_67890',
        isActive: true,
        statistics: { todayVisits: 89, totalVisits: 8960 },
        createdAt: '2024-01-14 14:20:00'
      }
    ]
    total.value = 2
  } catch (error) {
    ElMessage.error('获取应用列表失败')
  } finally {
    loading.value = false
  }
}

const handleReset = () => {
  Object.assign(queryForm, {
    pageNum: 1,
    pageSize: 10,
    keyword: '',
    protocolType: '',
    groupId: null,
    isActive: null
  })
  handleSearch()
}

const handleCreate = () => {
  console.log('handleCreate called')
  resetFormData()
  console.log('dialogVisible before:', dialogVisible.value)
  dialogVisible.value = true
  console.log('dialogVisible after:', dialogVisible.value)
  // 强制触发响应式更新
  nextTick(() => {
    console.log('nextTick - dialogVisible:', dialogVisible.value)
  })
}

const testDialog = () => {
  console.log('testDialog called')
  dialogVisible.value = !dialogVisible.value
  console.log('dialogVisible toggled to:', dialogVisible.value)
}

const handleEdit = (row: any) => {
  Object.assign(formData, row)
  dialogVisible.value = true
}

const handleView = (row: any) => {
  // TODO: 查看应用详情
  console.log('查看应用:', row)
}

const handleConfig = (row: any) => {
  // TODO: 应用配置
  console.log('配置应用:', row)
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除应用 "${row.name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // TODO: 调用删除API
    ElMessage.success('删除成功')
    handleSearch()
  } catch (error) {
    // 用户取消
  }
}

const handleStatusChange = async (row: any) => {
  try {
    // TODO: 调用状态切换API
    ElMessage.success(row.isActive ? '应用已启用' : '应用已禁用')
  } catch (error) {
    row.isActive = !row.isActive
    ElMessage.error('状态切换失败')
  }
}

const handleExport = () => {
  // TODO: 导出应用列表
  ElMessage.info('导出功能开发中')
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true

    console.log('提交表单:', formData)

    // 调用创建/更新API
    if (formData.id) {
      // 更新应用
      await updateApplication(formData)
      ElMessage.success('更新成功')
    } else {
      // 创建应用
      await createApplication(formData)
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    handleSearch()
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error(formData.id ? '更新失败' : '创建失败')
  } finally {
    submitLoading.value = false
  }
}

const handleDialogClose = () => {
  formRef.value?.resetFields()
  resetFormData()
}

const resetFormData = () => {
  Object.assign(formData, {
    id: null,
    name: '',
    description: '',
    url: '',
    logo: '',
    protocolType: '',
    redirectUri: '',
    scope: '',
    groupId: null,
    sort: 0,
    isActive: true
  })
}

const getProtocolTagType = (protocolType: string) => {
  const typeMap: Record<string, string> = {
    oauth2: 'primary',
    cas: 'success',
    saml: 'warning',
    oidc: 'info'
  }
  return typeMap[protocolType] || ''
}

const getProtocolName = (protocolType: string) => {
  const nameMap: Record<string, string> = {
    oauth2: 'OAuth2.0',
    cas: 'CAS',
    saml: 'SAML',
    oidc: 'OIDC'
  }
  return nameMap[protocolType] || protocolType
}

onMounted(() => {
  handleSearch()
})
</script>

<style lang="scss" scoped>
.application-list {
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px;
    background: #fff;
    border-radius: 8px;

    .search-section {
      display: flex;
      align-items: center;
    }

    .action-section {
      display: flex;
      gap: 8px;
    }
  }

  .app-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .app-icon {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
      background: #f5f7fa;

      img {
        width: 32px;
        height: 32px;
        border-radius: 4px;
      }
    }

    .app-details {
      .app-name {
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
      }

      .app-description {
        font-size: 12px;
        color: #909399;
        line-height: 1.2;
      }
    }
  }

  .stats-info {
    .stat-item {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      margin-bottom: 2px;

      .stat-label {
        color: #909399;
      }

      .stat-value {
        color: #303133;
        font-weight: 500;
      }
    }
  }

  .pagination {
    display: flex;
    justify-content: center;
    margin-top: 16px;
  }
}
</style>
