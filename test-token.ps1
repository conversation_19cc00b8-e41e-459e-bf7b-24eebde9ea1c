$body = @{
    grant_type = "authorization_code"
    code = "12a47f8848d44abaa3c43eedb20c3500"
    client_id = "oa-system-demo"
    client_secret = "demo-secret-123"
    redirect_uri = "http://localhost:3001/callback"
} | ConvertTo-<PERSON>son

try {
    Write-Host "Sending request to: http://localhost:9002/api/v1/oauth/token"
    Write-Host "Request body: $body"
    
    $response = Invoke-RestMethod -Uri "http://localhost:9002/api/v1/oauth/token" -Method POST -ContentType "application/json" -Body $body
    Write-Host "Success response:"
    $response | ConvertTo-Json -Depth 10
} catch {
    Write-Host "Error occurred:"
    Write-Host "Status Code: $($_.Exception.Response.StatusCode.value__)"
    Write-Host "Status Description: $($_.Exception.Response.StatusDescription)"
    Write-Host "Error Message: $($_.Exception.Message)"
    
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody"
    }
}
