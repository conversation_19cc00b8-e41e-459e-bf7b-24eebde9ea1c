{"version": 3, "file": "restrict-template-expressions.js", "sourceRoot": "", "sources": ["../../src/rules/restrict-template-expressions.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,+CAAiC;AAEjC,kCAQiB;AAejB,kBAAe,IAAA,iBAAU,EAAqB;IAC5C,IAAI,EAAE,+BAA+B;IACrC,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,6DAA6D;YAC/D,WAAW,EAAE,aAAa;YAC1B,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,WAAW,EAAE,yDAAyD;SACvE;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,QAAQ,EAAE;wBACR,WAAW,EACT,8DAA8D;wBAChE,IAAI,EAAE,SAAS;qBAChB;oBACD,YAAY,EAAE;wBACZ,WAAW,EACT,kEAAkE;wBACpE,IAAI,EAAE,SAAS;qBAChB;oBACD,YAAY,EAAE;wBACZ,WAAW,EACT,kEAAkE;wBACpE,IAAI,EAAE,SAAS;qBAChB;oBACD,WAAW,EAAE;wBACX,WAAW,EACT,iEAAiE;wBACnE,IAAI,EAAE,SAAS;qBAChB;oBACD,WAAW,EAAE;wBACX,WAAW,EACT,iEAAiE;wBACnE,IAAI,EAAE,SAAS;qBAChB;oBACD,UAAU,EAAE;wBACV,WAAW,EACT,gEAAgE;wBAClE,IAAI,EAAE,SAAS;qBAChB;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,IAAI;YAClB,YAAY,EAAE,IAAI;YAClB,WAAW,EAAE,IAAI;YACjB,WAAW,EAAE,IAAI;SAClB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAElD,SAAS,yBAAyB,CAAC,IAAa;YAC9C,IAAI,IAAA,oBAAa,EAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,CAAC;gBACjD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IACE,OAAO,CAAC,WAAW;gBACnB,IAAA,oBAAa,EAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,UAAU,GAAG,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,EACtE,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IACE,OAAO,CAAC,YAAY;gBACpB,IAAA,oBAAa,EAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,EAC7C,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAA,oBAAa,EAAC,IAAI,CAAC,EAAE,CAAC;gBAC5C,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,OAAO,CAAC,WAAW,IAAI,IAAA,kBAAW,EAAC,OAAO,EAAE,IAAI,CAAC,KAAK,QAAQ,EAAE,CAAC;gBACnE,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IACE,OAAO,CAAC,YAAY;gBACpB,IAAA,oBAAa,EAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,GAAG,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,EAC/D,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,OAAO,CAAC,UAAU,IAAI,IAAA,sBAAe,EAAC,IAAI,CAAC,EAAE,CAAC;gBAChD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO;YACL,eAAe,CAAC,IAA8B;gBAC5C,uCAAuC;gBACvC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,wBAAwB,EAAE,CAAC;oBACjE,OAAO;gBACT,CAAC;gBAED,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBAC1C,MAAM,cAAc,GAAG,IAAA,mCAA4B,EACjD,QAAQ,EACR,UAAU,CACX,CAAC;oBAEF,IACE,CAAC,sCAAsC,CACrC,cAAc,EACd,yBAAyB,CAC1B,EACD,CAAC;wBACD,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE,UAAU;4BAChB,SAAS,EAAE,aAAa;4BACxB,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC,EAAE;yBACrD,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;SACF,CAAC;QAEF,SAAS,sCAAsC,CAC7C,IAAa,EACb,SAA+C;YAE/C,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC;YAEjB,SAAS,GAAG,CAAC,SAAkB;gBAC7B,IAAI,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC;oBACxB,OAAO,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACpC,CAAC;gBAED,IAAI,SAAS,CAAC,cAAc,EAAE,EAAE,CAAC;oBAC/B,OAAO,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACnC,CAAC;gBAED,OAAO,SAAS,CAAC,SAAS,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;IACH,CAAC;CACF,CAAC,CAAC"}