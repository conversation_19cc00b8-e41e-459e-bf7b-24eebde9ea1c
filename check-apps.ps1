# 检查应用列表
try {
    # 登录获取token
    $loginData = @{
        username = "admin"
        password = "admin123"
    }
    
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:9002/api/v1/auth/login" -Method POST -ContentType "application/json" -Body ($loginData | ConvertTo-Json)
    Write-Host "Login successful"
    
    $token = $loginResponse.data.accessToken
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    # 获取应用列表
    $appsResponse = Invoke-RestMethod -Uri "http://localhost:9002/api/v1/sites" -Method GET -Headers $headers
    Write-Host "Found $($appsResponse.data.Count) applications:"
    
    foreach ($app in $appsResponse.data) {
        Write-Host "ID: $($app.id), Name: $($app.name), ClientID: $($app.client_id), Protocol: $($app.protocol_type), URL: $($app.redirect_uri)"
    }

} catch {
    Write-Host "Error: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode.value__)"
    }
}
