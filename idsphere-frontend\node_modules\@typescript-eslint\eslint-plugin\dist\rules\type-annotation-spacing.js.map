{"version": 3, "file": "type-annotation-spacing.js", "sourceRoot": "", "sources": ["../../src/rules/type-annotation-spacing.ts"], "names": [], "mappings": ";;AACA,wEAAsE;AAEtE,kCASiB;AA8BjB,SAAS,WAAW,CAAC,OAAgB;IACnC,MAAM,OAAO,GAAG;QACd,GAAG,CAAC,OAAO,EAAE,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACpE,GAAG,CAAC,OAAO,EAAE,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;KAClE,CAAC;IACF,MAAM,QAAQ,GAAG,OAAO,EAAE,SAAS,IAAI,EAAE,CAAC;IAC1C,MAAM,KAAK,GAAG;QACZ,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE;QACjC,GAAG,OAAO;QACV,GAAG,QAAQ,CAAC,KAAK;KAClB,CAAC;IACF,MAAM,KAAK,GAAG;QACZ,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE;QAChC,GAAG,OAAO;QACV,GAAG,QAAQ,CAAC,KAAK;KAClB,CAAC;IAEF,OAAO;QACL,KAAK,EAAE,KAAK;QACZ,KAAK,EAAE,KAAK;QACZ,QAAQ,EAAE,EAAE,GAAG,KAAK,EAAE,GAAG,QAAQ,CAAC,QAAQ,EAAE;QAC5C,QAAQ,EAAE,EAAE,GAAG,KAAK,EAAE,GAAG,QAAQ,CAAC,QAAQ,EAAE;QAC5C,SAAS,EAAE,EAAE,GAAG,KAAK,EAAE,GAAG,QAAQ,CAAC,SAAS,EAAE;QAC9C,UAAU,EAAE,EAAE,GAAG,KAAK,EAAE,GAAG,QAAQ,CAAC,UAAU,EAAE;KACjD,CAAC;AACJ,CAAC;AAED,SAAS,kBAAkB,CACzB,KAAsB,EACtB,IAA+B;IAE/B,MAAM,KAAK,GAAG,IAAI,EAAE,MAAM,CAAC;IAE3B,IAAI,IAAA,2BAAoB,EAAC,KAAK,CAAC,EAAE,CAAC;QAChC,OAAO,KAAK,CAAC,QAAQ,CAAC;IACxB,CAAC;SAAM,IAAI,IAAA,+BAAwB,EAAC,KAAK,CAAC,EAAE,CAAC;QAC3C,OAAO,KAAK,CAAC,SAAS,CAAC;IACzB,CAAC;IACD,OAAO,KAAK,CAAC,KAAK,CAAC;AACrB,CAAC;AAED,SAAS,QAAQ,CACf,KAAsB,EACtB,IAAuB;IAEvB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAEjC,IAAI,IAAA,uBAAgB,EAAC,KAAK,CAAC,IAAI,IAAA,0BAAmB,EAAC,KAAK,CAAC,EAAE,CAAC;QAC1D,OAAO,KAAK,CAAC,KAAK,CAAC;IACrB,CAAC;SAAM,IAAI,IAAA,mBAAY,EAAC,KAAK,CAAC,EAAE,CAAC;QAC/B,OAAO,kBAAkB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC1C,CAAC;SAAM,IAAI,IAAA,2BAAoB,EAAC,KAAK,CAAC,EAAE,CAAC;QACvC,OAAO,KAAK,CAAC,QAAQ,CAAC;IACxB,CAAC;SAAM,IAAI,IAAA,iBAAU,EAAC,KAAK,CAAC,EAAE,CAAC;QAC7B,OAAO,KAAK,CAAC,UAAU,CAAC;IAC1B,CAAC;IACD,OAAO,KAAK,CAAC,KAAK,CAAC;AACrB,CAAC;AAED,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,yBAAyB;IAC/B,IAAI,EAAE;QACJ,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE,CAAC,uCAAuC,CAAC;QACrD,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE;YACJ,WAAW,EAAE,oDAAoD;SAClE;QACD,OAAO,EAAE,YAAY;QACrB,QAAQ,EAAE;YACR,kBAAkB,EAAE,wCAAwC;YAC5D,mBAAmB,EAAE,yCAAyC;YAC9D,oBAAoB,EAAE,wCAAwC;YAC9D,qBAAqB,EAAE,yCAAyC;YAChE,sBAAsB,EACpB,sEAAsE;SACzE;QACD,MAAM,EAAE;YACN;gBACE,KAAK,EAAE;oBACL,aAAa,EAAE;wBACb,IAAI,EAAE,QAAQ;wBACd,UAAU,EAAE;4BACV,MAAM,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;4BAC3B,KAAK,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;yBAC3B;wBACD,oBAAoB,EAAE,KAAK;qBAC5B;iBACF;gBACD,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,MAAM,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oBAC3B,KAAK,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oBAC1B,SAAS,EAAE;wBACT,IAAI,EAAE,QAAQ;wBACd,UAAU,EAAE;4BACV,KAAK,EAAE,EAAE,IAAI,EAAE,+BAA+B,EAAE;4BAChD,KAAK,EAAE,EAAE,IAAI,EAAE,+BAA+B,EAAE;4BAChD,QAAQ,EAAE,EAAE,IAAI,EAAE,+BAA+B,EAAE;4BACnD,SAAS,EAAE,EAAE,IAAI,EAAE,+BAA+B,EAAE;4BACpD,QAAQ,EAAE,EAAE,IAAI,EAAE,+BAA+B,EAAE;4BACnD,UAAU,EAAE,EAAE,IAAI,EAAE,+BAA+B,EAAE;yBACtD;wBACD,oBAAoB,EAAE,KAAK;qBAC5B;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE;QACd,yDAAyD;QACzD,kEAAkE;QAClE,EAAE;KACH;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,WAAW,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAChC,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;QAE1C,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;QAErC;;;WAGG;QACH,SAAS,0BAA0B,CACjC,cAAiC;YAEjC,MAAM,SAAS,GAAG,cAAc,CAAC;YACjC,MAAM,kBAAkB,GAAG,UAAU,CAAC,cAAc,CAAC,SAAS,CAAE,CAAC;YACjE,IAAI,oBAAoB,GAAG,kBAAkB,CAAC;YAC9C,IAAI,aAAa,GAAG,UAAU,CAAC,cAAc,CAAC,kBAAkB,CAAE,CAAC;YACnE,IAAI,IAAI,GAAG,kBAAkB,CAAC,KAAK,CAAC;YAEpC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBAChC,OAAO;YACT,CAAC;YAED,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,QAAQ,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;YAE5D,IAAI,IAAI,KAAK,GAAG,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,EAAE,CAAC;gBAChD;gBACE,yGAAyG;gBACzG,UAAU,CAAC,oBAAoB,CAAC,aAAa,EAAE,oBAAoB,CAAC,EACpE,CAAC;oBACD,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,oBAAoB;wBAC1B,SAAS,EAAE,wBAAwB;wBACnC,IAAI,EAAE;4BACJ,IAAI;4BACJ,aAAa,EAAE,aAAa,CAAC,KAAK;yBACnC;wBACD,GAAG,CAAC,KAAK;4BACP,OAAO,KAAK,CAAC,WAAW,CAAC;gCACvB,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;gCACtB,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;6BAC9B,CAAC,CAAC;wBACL,CAAC;qBACF,CAAC,CAAC;gBACL,CAAC;gBAED,2BAA2B;gBAC3B,IAAI,GAAG,IAAI,CAAC;gBACZ,oBAAoB,GAAG,aAAa,CAAC;gBACrC,aAAa,GAAG,UAAU,CAAC,cAAc,CAAC,aAAa,CAAE,CAAC;gBAE1D,+DAA+D;gBAC/D,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,EAAE,CAAC;oBAC/D,IAAI,GAAG,GAAG,aAAa,CAAC,KAAK,IAAI,CAAC;oBAClC,oBAAoB,GAAG,aAAa,CAAC;oBACrC,aAAa,GAAG,UAAU,CAAC,cAAc,CAAC,aAAa,CAAE,CAAC;gBAC5D,CAAC;YACH,CAAC;YAED,MAAM,aAAa,GACjB,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACzD,MAAM,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAEnE,IAAI,KAAK,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;gBAC7B,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,kBAAkB;oBACxB,SAAS,EAAE,oBAAoB;oBAC/B,IAAI,EAAE;wBACJ,IAAI;qBACL;oBACD,GAAG,CAAC,KAAK;wBACP,OAAO,KAAK,CAAC,eAAe,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC;oBACxD,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,CAAC,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;gBACnC,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,kBAAkB;oBACxB,SAAS,EAAE,sBAAsB;oBACjC,IAAI,EAAE;wBACJ,IAAI;qBACL;oBACD,GAAG,CAAC,KAAK;wBACP,OAAO,KAAK,CAAC,WAAW,CAAC;4BACvB,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;4BAC3B,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;yBACnB,CAAC,CAAC;oBACL,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;YAED,IAAI,MAAM,IAAI,aAAa,KAAK,CAAC,EAAE,CAAC;gBAClC,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,oBAAoB;oBAC1B,SAAS,EAAE,qBAAqB;oBAChC,IAAI,EAAE;wBACJ,IAAI;qBACL;oBACD,GAAG,CAAC,KAAK;wBACP,OAAO,KAAK,CAAC,eAAe,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;oBACnD,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,CAAC,MAAM,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;gBACxC,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,oBAAoB;oBAC1B,SAAS,EAAE,uBAAuB;oBAClC,IAAI,EAAE;wBACJ,IAAI;qBACL;oBACD,GAAG,CAAC,KAAK;wBACP,OAAO,KAAK,CAAC,WAAW,CAAC;4BACvB,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;4BACtB,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;yBAC9B,CAAC,CAAC;oBACL,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO;YACL,YAAY,CAAC,IAAI;gBACf,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;oBACxB,0BAA0B,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAClD,CAAC;YACH,CAAC;YACD,gBAAgB,CAAC,IAAI;gBACnB,0BAA0B,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAClD,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}