import request from '@/utils/request'

// 应用相关接口
export interface Application {
  id?: number
  name: string
  description?: string
  url: string
  logo?: string
  favicon?: string
  protocolType: string
  redirectUri?: string
  scope?: string
  clientId?: string
  clientSecret?: string
  isActive: boolean
  sort: number
  groupId?: number
  createdAt?: string
  updatedAt?: string
  deleted?: boolean
}

export interface ApplicationQueryParams {
  pageNum: number
  pageSize: number
  keyword?: string
  protocolType?: string
  groupId?: number
  isActive?: boolean
  sortField?: string
  sortOrder?: string
}

export interface ApplicationCreateParams {
  name: string
  description?: string
  url: string
  logo?: string
  favicon?: string
  protocolType: string
  redirectUri?: string
  scope?: string
  groupId?: number
  sort?: number
  isActive?: boolean
}

export interface ApplicationUpdateParams extends ApplicationCreateParams {
  id: number
}

// 获取应用列表
export const getApplicationList = (params: ApplicationQueryParams) => {
  return request({
    url: '/sites',
    method: 'get',
    params
  })
}

// 获取应用详情
export const getApplicationById = (id: number) => {
  return request({
    url: `/sites/${id}`,
    method: 'get'
  })
}

// 创建应用
export const createApplication = (data: ApplicationCreateParams) => {
  return request({
    url: '/sites',
    method: 'post',
    data
  })
}

// 更新应用
export const updateApplication = (data: ApplicationUpdateParams) => {
  return request({
    url: `/sites/${data.id}`,
    method: 'put',
    data
  })
}

// 删除应用
export const deleteApplication = (id: number) => {
  return request({
    url: `/sites/${id}`,
    method: 'delete'
  })
}

// 切换应用状态
export const toggleApplicationStatus = (id: number, isActive: boolean) => {
  return request({
    url: `/sites/${id}/status`,
    method: 'put',
    params: { isActive }
  })
}

// 获取用户可访问的应用
export const getUserAccessibleApplications = () => {
  return request({
    url: '/sites/user-accessible',
    method: 'get'
  })
}

// 获取应用分组
export const getApplicationGroups = () => {
  return request({
    url: '/sites/groups',
    method: 'get'
  })
}

// 创建应用分组
export const createApplicationGroup = (data: { name: string; description?: string }) => {
  return request({
    url: '/sites/groups',
    method: 'post',
    data
  })
}

// 更新应用分组
export const updateApplicationGroup = (id: number, data: { name: string; description?: string }) => {
  return request({
    url: `/sites/groups/${id}`,
    method: 'put',
    data
  })
}

// 删除应用分组
export const deleteApplicationGroup = (id: number) => {
  return request({
    url: `/sites/groups/${id}`,
    method: 'delete'
  })
}

// 获取应用统计信息
export const getApplicationStatistics = (id: number) => {
  return request({
    url: `/sites/${id}/statistics`,
    method: 'get'
  })
}

// 生成客户端ID
export const generateClientId = () => {
  return request({
    url: '/sites/generate-client-id',
    method: 'post'
  })
}

// 生成客户端密钥
export const generateClientSecret = () => {
  return request({
    url: '/sites/generate-client-secret',
    method: 'post'
  })
}

// 记录应用访问
export const recordApplicationAccess = (applicationId: number) => {
  return request({
    url: `/sites/${applicationId}/access`,
    method: 'post'
  })
}
